extends Control

# UI elements (you'll need to create these in the scene)
@onready var stats_label: Label
@onready var npc_name_label: Label  
@onready var question_label: Label
@onready var yes_button: Button
@onready var no_button: Button
@onready var day_label: Label

@onready var game_manager = GameManager

func _ready():
	# Connect to game manager signals
	game_manager.stats_updated.connect(_on_stats_updated)
	game_manager.new_question.connect(_on_new_question)
	game_manager.day_ended.connect(_on_day_ended)
	game_manager.game_ended.connect(_on_game_ended)
	
	# Connect button signals (if you have UI buttons)
	if yes_button:
		yes_button.pressed.connect(_on_yes_pressed)
	if no_button:
		no_button.pressed.connect(_on_no_pressed)
	
	print("Demo UI ready! The game will start automatically.")

func _on_stats_updated(stats):
	if stats_label:
		var stats_text = "Population: %d\nHappiness: %d\nTreasury: %d gold\nMilitary: %d\nFood: %d" % [
			stats.population, stats.happiness, stats.treasury, stats.military, stats.food
		]
		stats_label.text = stats_text

func _on_new_question(npc_name: String, question: String, context: String):
	if npc_name_label:
		npc_name_label.text = npc_name
	if question_label:
		question_label.text = question
	if day_label:
		day_label.text = "Day %d" % game_manager.current_day
	
	# Enable decision buttons
	if yes_button:
		yes_button.disabled = false
	if no_button:
		no_button.disabled = false

func _on_day_ended(day_number: int):
	print("Day %d has ended!" % day_number)

func _on_game_ended(reason: String):
	print("Game ended: %s" % reason)
	# Disable buttons
	if yes_button:
		yes_button.disabled = true
	if no_button:
		no_button.disabled = true

func _on_yes_pressed():
	game_manager.make_decision("YES")
	_disable_buttons()

func _on_no_pressed():
	game_manager.make_decision("NO")
	_disable_buttons()

func _disable_buttons():
	if yes_button:
		yes_button.disabled = true
	if no_button:
		no_button.disabled = true

# Handle keyboard input for console-style play
func _input(event):
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_Y:
			game_manager.make_decision("YES")
		elif event.keycode == KEY_N:
			game_manager.make_decision("NO")
