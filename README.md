# Sort the Court: AI Edition

A medieval kingdom management game inspired by "Sort the Court" where you play as a king making decisions for AI-generated NPCs.

## Features

- **AI-Generated NPCs**: Each character is created by AI with unique names, personalities, and requests
- **Dynamic Storytelling**: NPCs present contextual requests based on current kingdom state
- **Kingdom Management**: Balance 5 key stats: Population, Happiness, Treasury, Military, and Food
- **Consequence System**: Every decision affects your kingdom's stats
- **Random Events**: Daily events add unpredictability to your rule
- **Victory/Defeat Conditions**: Multiple ways to win or lose based on your performance

## How to Play

1. **Setup**: Make sure you have a local AI server running on `http://127.0.0.1:4315` (the game uses this for AI generation)

2. **Gameplay**: 
   - Each day, 5 NPCs will approach your throne
   - Listen to their requests and respond with YES or NO
   - Watch how your decisions affect kingdom stats
   - Survive random events and try to build a thriving kingdom

3. **Controls**:
   - Console: The game runs automatically and shows output in the Godot console
   - UI: Connect the demo_ui.gd script to create a visual interface
   - Keyboard: Press Y for YES, N for NO (if using the UI script)

## Game Components

### GameManager (`game manager/game manager.gd`)
The main game controller that handles:
- Day progression and NPC generation
- AI communication for character creation
- Decision processing and consequence application
- Game state management and win/lose conditions

### Key Functions:
- `start_new_day()`: Begins a new day with fresh NPCs
- `generate_random_npc()`: Creates AI-powered characters
- `make_decision(decision: String)`: Process player choices
- `get_game_state()`: Get current game information

### Player2 (`player2/player2.gd`)
AI service manager that:
- Connects to local AI server (port 4315)
- Handles chat completions for NPC generation
- Manages AI conversation history

### CharacterManager (`character_manager/character_manager.gd`)
Character storage system that:
- Creates and stores NPC data
- Maintains chat history for each character
- Provides character lookup functionality

## Kingdom Stats

- **Population**: Affects kingdom size and tax income
- **Happiness**: Affects loyalty and productivity
- **Treasury**: Gold for funding projects and military
- **Military**: Defense against threats
- **Food**: Keeps population healthy and growing

## NPC Types

The game generates 10 different types of NPCs:
- **Merchant**: Trade and economic requests
- **Farmer**: Agriculture and food-related issues
- **Soldier**: Military and defense matters
- **Noble**: Political and social requests
- **Peasant**: Common people's concerns
- **Wizard**: Magical and mystical requests
- **Priest**: Religious and moral guidance
- **Thief**: Criminal justice decisions
- **Diplomat**: Foreign relations
- **Inventor**: Innovation and technology

## Win/Lose Conditions

### Game Over:
- Population reaches 0
- Happiness reaches 0 (revolution)
- Treasury drops below -500 (bankruptcy)
- No military with large population (conquest)
- No food with large population (famine)

### Victory:
- Survive 30+ days with Population ≥ 200 and Happiness ≥ 80

## Setup Instructions

1. **AI Server**: Ensure you have a compatible AI server running on port 4315
2. **Godot**: Open the project in Godot 4.x
3. **Run**: The game starts automatically when you run the main scene
4. **UI (Optional)**: Attach the demo_ui.gd script to a Control node for visual interface

## Customization

### Adding New NPC Types:
Edit the `npc_types` array in `generate_random_npc()` and add corresponding consequence logic in `apply_decision_consequences()`.

### Modifying Stats:
Adjust the `kingdom_stats` dictionary and consequence values to balance gameplay.

### Custom Events:
Add new random events in the `apply_random_daily_events()` function.

## Technical Requirements

- Godot 4.x
- Local AI server compatible with OpenAI API format
- HTTP request capabilities

## Signals for UI Integration

The GameManager emits these signals for UI updates:
- `stats_updated(stats)`: When kingdom stats change
- `new_question(npc_name, question, context)`: When NPC presents request
- `day_ended(day_number)`: When day concludes
- `game_ended(reason)`: When game ends (win/lose)

Connect these signals to your UI elements for a complete visual experience!
