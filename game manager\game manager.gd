extends Node

# References to other managers
@onready var ai = Player2
@onready var character_manager = CharacterManager

# Game state variables
var current_day = 1
var kingdom_stats = {
	"population": 100,
	"happiness": 50,
	"treasury": 1000,
	"military": 50,
	"food": 50
}

# Story and narrative system
var story_state = {
	"main_plot": "beginning",  # beginning, rising_action, climax, resolution
	"story_flags": {},  # Track important story decisions
	"character_relationships": {},  # Track relationships with recurring characters
	"major_events": [],  # Record of significant events
	"kingdom_reputation": "neutral",  # just, tyrannical, wise, weak, etc.
	"ending_path": "undetermined"  # Track which ending we're heading toward
}

# Decision tracking for sophisticated consequences
var decision_history = []
var recent_decisions = []  # Last 5 decisions for context
var npc_memory = {}  # NPCs remember past interactions

var current_npc = null
var current_question = ""
var current_context = ""
var current_ai_response = ""  # Store full AI response for analysis
var npcs_today = []
var npcs_per_day = 3  # Reduced from 5 to 3 to help with server queue limits
var game_over = false

# Player decision waiting system
var waiting_for_decision = false
var player_decision = ""

# Pre-generated content system
var pregenerated_npcs = []  # All NPCs for the entire game
var total_days = 10  # Generate content for 10 days
var current_npc_index = 0

# Story progression tracking
var story_milestones = {
	"first_major_crisis": false,
	"economic_turning_point": false,
	"military_conflict": false,
	"diplomatic_achievement": false,
	"moral_crossroads": false
}

# Signals for UI updates (you can connect these to your UI)
signal stats_updated(stats)
signal new_question(npc_name, question, context)
signal day_ended(day_number)
signal game_ended(reason)
signal story_event(event_type, description)
signal relationship_changed(character, relationship_type)

func _ready():
	print("=== SORT THE COURT: AI EDITION ===")
	print("You are the King! Make wise decisions to keep your kingdom prosperous.")
	print("Answer YES or NO to each petitioner's request.")
	print("")

	# Initialize story system
	initialize_story()

	# Pre-generate all NPCs and their requests
	await pregenerate_all_content()

	await start_new_day()

# Initialize the main storyline and story system
func initialize_story():
	print("=== THE KINGDOM OF VALDRIS ===")
	print("Your father, the late King Aldwin, has passed away unexpectedly.")
	print("You have inherited a kingdom in turmoil. Dark clouds gather on the horizon...")
	print("Your decisions will shape not only the kingdom's fate, but the very story of your reign.")
	print("")

	# Set initial story state
	story_state.main_plot = "beginning"
	story_state.story_flags["inherited_throne"] = true
	story_state.major_events.append({
		"day": 0,
		"event": "Coronation",
		"description": "Inherited the throne of Valdris after father's death"
	})

	# Initialize kingdom reputation based on starting stats
	update_kingdom_reputation()

	story_event.emit("story_beginning", "The reign of the new monarch begins...")

# Pre-generate all NPCs and their requests for the entire game
func pregenerate_all_content():
	print("=== GENERATING GAME CONTENT ===")
	print("This may take a moment, but will make gameplay much faster...")

	var total_npcs = total_days * npcs_per_day
	pregenerated_npcs.clear()

	for i in range(total_npcs):
		print("Generating NPC %d/%d..." % [i + 1, total_npcs])

		# Generate basic NPC info
		var npc_types = [
			"merchant", "farmer", "soldier", "noble", "peasant",
			"wizard", "priest", "thief", "diplomat", "inventor"
		]
		var npc_type = npc_types.pick_random()
		var npc_name = await generate_npc_name(npc_type)

		# Create character description
		var description = create_npc_description(npc_type, npc_name)

		# Create the character in character manager
		character_manager.create_new_character(npc_name, description)

		# Get the character and generate their request
		var character = null
		for char in character_manager.characters:
			if char.name == npc_name:
				character = char
				break

		var temp_history = character.chat_history
		temp_history.append({"role": "user", "content": "present your request"})

		# Generate the AI request
		var npc_message = await ai.create_chat_completion(temp_history, "")

		# Pre-analyze consequences for both YES and NO decisions
		print("  - Analyzing YES consequences...")
		var yes_analysis = await analyze_decision_consequences_for_pregeneration(npc_name, npc_type, npc_message, true)

		print("  - Analyzing NO consequences...")
		var no_analysis = await analyze_decision_consequences_for_pregeneration(npc_name, npc_type, npc_message, false)

		# Store the complete NPC data with pre-analyzed consequences
		var complete_npc = {
			"name": npc_name,
			"type": npc_type,
			"description": description,
			"request": npc_message,
			"character": character,
			"yes_consequences": yes_analysis,
			"no_consequences": no_analysis
		}

		pregenerated_npcs.append(complete_npc)

		# Small delay to prevent overwhelming the AI
		if i < total_npcs - 1:
			await get_tree().create_timer(1.0).timeout  # Increased delay for consequence analysis

	print("=== CONTENT GENERATION COMPLETE ===")
	print("Generated %d NPCs with full consequence analysis!" % total_npcs)
	print("- %d NPCs for %d days of gameplay" % [total_npcs, total_days])
	print("- %d YES consequences pre-analyzed" % total_npcs)
	print("- %d NO consequences pre-analyzed" % total_npcs)
	print("Game will now run at full speed with instant decisions!")
	print("")

# Analyze consequences during pre-generation (simplified for batch processing)
func analyze_decision_consequences_for_pregeneration(npc_name: String, npc_type: String, npc_request: String, is_yes: bool):
	var analysis_prompt = """Analyze this royal decision and respond with JSON:

NPC: %s (%s)
Request: "%s"
Decision: %s
Current Stats: Pop:%d, Happy:%d, Gold:%d, Military:%d, Food:%d

Respond with this JSON format only, no additional explanation:
{
  "stat_changes": {
    "population": 0,
    "happiness": 10,
    "treasury": -50,
    "military": 0,
    "food": 0
  },
  "story_impact": "Brief description"
}

Keep stat changes realistic (-50 to +50).""" % [
		npc_name, npc_type, npc_request,
		"YES" if is_yes else "NO",
		kingdom_stats.population, kingdom_stats.happiness,
		kingdom_stats.treasury, kingdom_stats.military, kingdom_stats.food
	]

	var result = await ai.create_chat_completion([
		{"role": "user", "content": "Analyze this decision"}
	], analysis_prompt)

	if result != "":
		var parsed = JSON.parse_string(result.strip_edges())
		if parsed != null:
			return parsed
		else:
			print("Failed to parse consequence analysis JSON during pregeneration")
			return create_fallback_analysis(npc_type, is_yes)
	else:
		print("Failed to get AI consequence analysis during pregeneration")
		return create_fallback_analysis(npc_type, is_yes)

# Create fallback analysis when AI fails during pregeneration
func create_fallback_analysis(npc_type: String, is_yes: bool):
	var stat_changes = {}

	# Simple fallback based on NPC type
	match npc_type:
		"merchant":
			stat_changes = {"treasury": 30, "happiness": 5} if is_yes else {"treasury": -10}
		"farmer":
			stat_changes = {"food": 15, "treasury": -20} if is_yes else {"food": -5, "happiness": -5}
		"soldier":
			stat_changes = {"military": 10, "treasury": -30} if is_yes else {"military": -5}
		"noble":
			stat_changes = {"happiness": 10, "treasury": -40} if is_yes else {"happiness": -10}
		"peasant":
			stat_changes = {"happiness": 15, "treasury": -15} if is_yes else {"happiness": -15}
		_:
			stat_changes = {"happiness": 5} if is_yes else {"happiness": -5}

	return {
		"stat_changes": stat_changes,
		"story_impact": "The King %s the %s's request" % ["granted" if is_yes else "denied", npc_type]
	}

# Start a new day with pre-generated NPCs
func start_new_day():
	print("=== DAY %d ===" % current_day)
	print_kingdom_stats()
	print("")

	# Check if we have enough pre-generated NPCs
	if current_npc_index + npcs_per_day > pregenerated_npcs.size():
		print("Warning: Running out of pre-generated NPCs!")
		print("Game will end after this day.")

	npcs_today.clear()

	# Get today's NPCs from pre-generated content
	print("Selecting today's petitioners from pre-generated content...")
	for i in range(npcs_per_day):
		if current_npc_index < pregenerated_npcs.size():
			var npc = pregenerated_npcs[current_npc_index]
			npcs_today.append(npc)
			current_npc_index += 1
			print("- %s the %s" % [npc.name, npc.type])
		else:
			print("No more pre-generated NPCs available!")
			break

	print("Ready to begin day %d with %d petitioners!" % [current_day, npcs_today.size()])
	print("")

	# Start processing NPCs (now instant!)
	await process_daily_npcs()

# This function is no longer used - NPCs are pre-generated

# Generate NPC name using AI
func generate_npc_name(npc_type: String) -> String:
	var name_prompt = "Generate a single medieval fantasy name for a %s. Respond with only the name, nothing else." % npc_type

	var result = await ai.create_chat_completion([
		{"role": "user", "content": "Generate a name for a %s" % npc_type}
	], name_prompt)

	if result != "":
		return result.strip_edges()
	else:
		# Fallback names if AI fails
		var fallback_names = ["Aldric", "Beatrice", "Cedric", "Diana", "Edmund", "Fiona"]
		return fallback_names.pick_random()

# Create simple NPC description for AI
func create_npc_description(npc_type: String, npc_name: String) -> String:
	var base_context = get_kingdom_context()

	var description = """You are %s, a %s in the Kingdom of Valdris.

Kingdom Stats: %s
Your Reputation: %s

Present ONE request to the King that requires a YES or NO answer. Keep it short (1-2 sentences). End with a clear question.

Example: "Your Majesty, the harvest has failed. Will you open the royal granaries to feed the people?"

Stay in character and be respectful.""" % [npc_name, npc_type, base_context, story_state.kingdom_reputation]

	return description

# Process all NPCs for the day
func process_daily_npcs():
	for npc in npcs_today:
		if game_over:
			break

		await present_npc_to_king(npc)

		# Small delay between NPCs
		await get_tree().create_timer(1.0).timeout

	# End of day
	await end_day()

# Present an NPC to the king using pre-generated request
func present_npc_to_king(npc):
	current_npc = npc
	print("--- %s the %s approaches the throne ---" % [npc.name, npc.type])

	# Use the pre-generated request (no AI call needed!)
	var npc_message = npc.request
	current_question = npc_message
	current_context = npc.description
	current_ai_response = npc_message  # Store for sophisticated analysis

	print("%s says:" % npc.name)
	print('"%s"' % npc_message)
	print("")
	print("What is your decision, Your Majesty? (YES/NO)")
	print("Click the YES or NO buttons to make your choice!")

	# Emit signal for UI
	new_question.emit(npc.name, npc_message, current_context)

	# Wait for player input (instant response now!)
	await wait_for_player_decision()

# Wait for player input (YES/NO)
func wait_for_player_decision():
	print("(Waiting for your decision...)")
	waiting_for_decision = true
	player_decision = ""

	# Wait until the player makes a decision via the buttons
	while waiting_for_decision:
		await get_tree().process_frame

	# Process the decision that was made
	await process_decision(player_decision)

# Process the king's decision
func process_decision(decision: String):
	var is_yes = decision.to_upper() == "YES"

	# Add the decision to character's chat history
	var character = null
	for c in character_manager.characters:
		if c.name == current_npc.name:
			character = c
			break

	if character:
		character.chat_history.append({
			"role": "user",
			"content": "The King's decision: %s" % decision
		})

	# Record the decision for history and context
	record_decision(current_npc, decision, current_ai_response)

	# Apply sophisticated consequences based on AI analysis
	await apply_sophisticated_consequences(current_npc, is_yes, current_ai_response)

	# Update stats display
	print("Kingdom stats after decision:")
	print_kingdom_stats()
	print("")

	# Check for game over conditions
	check_game_over()

# Record decision in history for context and story progression
func record_decision(npc, decision: String, ai_request: String):
	var decision_record = {
		"day": current_day,
		"npc_name": npc.name,
		"npc_type": npc.type,
		"request": ai_request,
		"decision": decision,
		"timestamp": Time.get_unix_time_from_system()
	}

	decision_history.append(decision_record)
	recent_decisions.append(decision_record)

	# Keep only last 5 decisions for recent context
	if recent_decisions.size() > 5:
		recent_decisions.pop_front()

	# Update NPC memory if they've appeared before
	if not npc_memory.has(npc.name):
		npc_memory[npc.name] = []
	npc_memory[npc.name].append(decision_record)

# Apply pre-analyzed consequences (instant!)
func apply_sophisticated_consequences(npc, is_yes: bool, _ai_request: String):
	print("Applying pre-analyzed consequences...")

	# Get the pre-analyzed consequences based on the decision
	var consequence_analysis = null
	if is_yes:
		consequence_analysis = npc.yes_consequences
	else:
		consequence_analysis = npc.no_consequences

	if consequence_analysis:
		print("Using pre-analyzed consequences - instant application!")
		# Apply stat changes from pre-analyzed data
		apply_analyzed_stat_changes(consequence_analysis)

		# Update story progression based on decision
		await update_story_progression(npc, is_yes, _ai_request, consequence_analysis)

		# Update character relationships
		update_character_relationships(npc, is_yes, consequence_analysis)

		# Check for story milestones
		check_story_milestones(consequence_analysis)
	else:
		print("No pre-analyzed consequences found - using fallback")
		# Fallback to basic consequences if pre-analysis missing
		apply_basic_consequences(npc, is_yes)

		# Still update story with basic info
		update_basic_story_progression(npc, is_yes, _ai_request)

# Use AI to analyze decision consequences
func analyze_decision_consequences(npc, is_yes: bool, ai_request: String):
	var analysis_prompt = """Analyze this royal decision and respond with JSON:

NPC: %s (%s)
Request: "%s"
Decision: %s
Current Stats: Pop:%d, Happy:%d, Gold:%d, Military:%d, Food:%d

Respond with this JSON format ONLY:
{
  "stat_changes": {
    "population": 0,
    "happiness": 10,
    "treasury": -50,
    "military": 0,
    "food": 0
  },
  "story_impact": "Brief description"
}
no additional explanation

Keep stat changes realistic (-50 to +50).""" % [
		npc.name, npc.type, ai_request,
		"YES" if is_yes else "NO",
		kingdom_stats.population, kingdom_stats.happiness,
		kingdom_stats.treasury, kingdom_stats.military, kingdom_stats.food
	]

	var result = await ai.create_chat_completion([
		{"role": "user", "content": "Analyze this royal decision"}
	], analysis_prompt)

	if result != "":
		var json_text = result.strip_edges()
		var parsed = JSON.parse_string(json_text)
		if parsed != null:
			return parsed
		else:
			print("Failed to parse consequence analysis JSON: %s" % json_text)
			return null
	else:
		print("Failed to get AI consequence analysis")
		return null

# Apply stat changes from AI analysis
func apply_analyzed_stat_changes(analysis):
	if analysis.has("stat_changes"):
		var changes = analysis["stat_changes"]
		for stat in changes:
			if kingdom_stats.has(stat):
				kingdom_stats[stat] += changes[stat]
				kingdom_stats[stat] = max(0, kingdom_stats[stat])  # Don't go below 0

		print("AI Analysis Applied:")
		for stat in changes:
			if changes[stat] != 0:
				var change_text = "+" if changes[stat] > 0 else ""
				print("  %s: %s%d" % [stat.capitalize(), change_text, changes[stat]])

		stats_updated.emit(kingdom_stats)

# Update story progression based on decision
func update_story_progression(npc, is_yes: bool, _ai_request: String, analysis):
	if analysis.has("story_impact"):
		var story_impact = analysis["story_impact"]
		print("Story Impact: %s" % story_impact)

		# Record major story event
		story_state.major_events.append({
			"day": current_day,
			"event": "%s's Request" % npc.name,
			"description": story_impact,
			"decision": "YES" if is_yes else "NO"
		})

		# Update story phase based on day and events
		update_story_phase()

		# Update kingdom reputation based on decisions
		update_kingdom_reputation()

		story_event.emit("decision_impact", story_impact)

# Update character relationships
func update_character_relationships(npc, _is_yes: bool, analysis):
	if analysis.has("relationship_change"):
		var relationship_change = analysis["relationship_change"]

		# Update relationship with this NPC type
		if not story_state.character_relationships.has(npc.type):
			story_state.character_relationships[npc.type] = "neutral"

		# Simple relationship tracking (could be more sophisticated)
		if "positive" in relationship_change.to_lower() or "pleased" in relationship_change.to_lower():
			story_state.character_relationships[npc.type] = "friendly"
		elif "negative" in relationship_change.to_lower() or "angry" in relationship_change.to_lower():
			story_state.character_relationships[npc.type] = "hostile"

		relationship_changed.emit(npc.type, story_state.character_relationships[npc.type])

# Check for story milestones
func check_story_milestones(analysis):
	if analysis.has("milestone_triggered") and analysis["milestone_triggered"] != "none":
		var milestone = analysis["milestone_triggered"]
		if story_milestones.has(milestone) and not story_milestones[milestone]:
			story_milestones[milestone] = true
			print("=== STORY MILESTONE REACHED: %s ===" % milestone.to_upper())
			story_event.emit("milestone", milestone)

# Fallback basic consequences if AI analysis fails
func apply_basic_consequences(npc, is_yes: bool):
	var stat_changes = {}

	# Simple fallback based on NPC type
	match npc.type:
		"merchant":
			stat_changes = {"treasury": 30, "happiness": 5} if is_yes else {"treasury": -10}
		"farmer":
			stat_changes = {"food": 15, "treasury": -20} if is_yes else {"food": -5, "happiness": -5}
		"soldier":
			stat_changes = {"military": 10, "treasury": -30} if is_yes else {"military": -5}
		"noble":
			stat_changes = {"happiness": 10, "treasury": -40} if is_yes else {"happiness": -10}
		"peasant":
			stat_changes = {"happiness": 15, "treasury": -15} if is_yes else {"happiness": -15}
		_:
			stat_changes = {"happiness": 5} if is_yes else {"happiness": -5}

	# Apply changes
	for stat in stat_changes:
		kingdom_stats[stat] += stat_changes[stat]
		kingdom_stats[stat] = max(0, kingdom_stats[stat])

	stats_updated.emit(kingdom_stats)

# Basic story progression when AI analysis fails
func update_basic_story_progression(npc, is_yes: bool, _ai_request: String):
	# Record the event even without AI analysis
	story_state.major_events.append({
		"day": current_day,
		"event": "%s's Request" % npc.name,
		"description": "The King %s %s the %s's request" % [
			"granted" if is_yes else "denied",
			"" if is_yes else "to",
			npc.type
		],
		"decision": "YES" if is_yes else "NO"
	})

	# Update story phase and reputation
	update_story_phase()
	update_kingdom_reputation()

	print("Basic story progression applied")

# Get story context for NPCs
func get_story_context() -> String:
	var context = "Story Phase: %s\n" % story_state.main_plot.replace("_", " ").capitalize()

	if story_state.major_events.size() > 0:
		context += "Recent Major Events:\n"
		var recent_events = story_state.major_events.slice(-3)  # Last 3 events
		for event in recent_events:
			context += "- Day %d: %s\n" % [event.day, event.description]

	if story_state.story_flags.size() > 0:
		context += "Important Story Flags: "
		var flags = []
		for flag in story_state.story_flags:
			if story_state.story_flags[flag]:
				flags.append(flag.replace("_", " "))
		context += ", ".join(flags) + "\n"

	return context

# Get reputation context for NPCs
func get_reputation_context() -> String:
	var reputation = story_state.kingdom_reputation
	var context = "The King is currently seen as: %s\n" % reputation.capitalize()

	# Add reputation details based on stats and decisions
	if kingdom_stats.happiness > 70:
		context += "The people generally love their ruler.\n"
	elif kingdom_stats.happiness < 30:
		context += "There is growing discontent among the populace.\n"

	if kingdom_stats.treasury > 1500:
		context += "The kingdom is known for its wealth.\n"
	elif kingdom_stats.treasury < 500:
		context += "The royal coffers are running low.\n"

	return context

# Get recent decisions context for NPCs
func get_recent_decisions_context() -> String:
	if recent_decisions.size() == 0:
		return "No recent major decisions to reference."

	var context = "Recent Royal Decisions:\n"
	for decision in recent_decisions:
		context += "- %s the %s: %s (Decision: %s)\n" % [
			decision.npc_name, decision.npc_type,
			decision.request.substr(0, 50) + "...", decision.decision
		]

	return context

# Update story phase based on progression
func update_story_phase():
	match current_day:
		1, 2, 3, 4, 5:
			story_state.main_plot = "beginning"
		6, 7, 8, 9, 10, 11, 12, 13, 14, 15:
			story_state.main_plot = "rising_action"
		16, 17, 18, 19, 20, 21, 22, 23, 24, 25:
			story_state.main_plot = "climax"
		_:
			story_state.main_plot = "resolution"

# Update kingdom reputation based on decisions and stats
func update_kingdom_reputation():
	var happiness = kingdom_stats.happiness
	var treasury = kingdom_stats.treasury
	var military = kingdom_stats.military
	var _population = kingdom_stats.population

	# Analyze recent decisions for reputation
	var recent_yes_count = 0
	var recent_no_count = 0

	for decision in recent_decisions:
		if decision.decision == "YES":
			recent_yes_count += 1
		else:
			recent_no_count += 1

	# Determine reputation based on stats and decision patterns
	if happiness > 70 and treasury > 1000:
		story_state.kingdom_reputation = "wise_and_prosperous"
	elif happiness > 70:
		story_state.kingdom_reputation = "beloved"
	elif military > 70 and recent_no_count > recent_yes_count:
		story_state.kingdom_reputation = "stern_but_strong"
	elif treasury > 1500 and recent_no_count > recent_yes_count:
		story_state.kingdom_reputation = "wealthy_but_stingy"
	elif recent_yes_count > recent_no_count * 2:
		story_state.kingdom_reputation = "generous"
	elif happiness < 30:
		story_state.kingdom_reputation = "unpopular"
	elif treasury < 300:
		story_state.kingdom_reputation = "struggling"
	else:
		story_state.kingdom_reputation = "balanced"

# End the current day
func end_day():
	print("=== END OF DAY %d ===" % current_day)
	print("Final kingdom stats:")
	print_kingdom_stats()
	print("")

	# Random events at end of day
	await apply_random_daily_events()

	# Emit day ended signal
	day_ended.emit(current_day)

	current_day += 1

	if not game_over:
		print("Press any key to continue to the next day...")
		await get_tree().create_timer(3.0).timeout  # Auto-continue for demo
		await start_new_day()

# Apply story-driven random events at the end of each day
func apply_random_daily_events():
	# 30% chance of a random event
	if randi() % 100 < 30:
		var events = get_contextual_events()

		var event = events.pick_random()
		print("STORY EVENT: %s!" % event.name)
		print(event.description)

		# Apply effects
		for stat in event.effects:
			kingdom_stats[stat] += event.effects[stat]
			kingdom_stats[stat] = max(0, kingdom_stats[stat])

		# Record as major event if significant
		if event.has("major") and event.major:
			story_state.major_events.append({
				"day": current_day,
				"event": event.name,
				"description": event.description
			})
			story_event.emit("random_event", event.description)

		print("Effects applied to kingdom.")
		print_kingdom_stats()
		print("")

# Get contextual events based on story state and kingdom condition
func get_contextual_events():
	var base_events = [
		{"name": "Good Harvest", "effects": {"food": 30, "happiness": 10},
		 "description": "The autumn harvest yields more grain than expected."},
		{"name": "Merchant Caravan", "effects": {"treasury": 100},
		 "description": "A wealthy merchant caravan pays taxes to pass through your lands."},
		{"name": "Festival Day", "effects": {"happiness": 20, "treasury": -30},
		 "description": "The people celebrate a traditional festival, boosting morale but costing the treasury."}
	]

	var contextual_events = []

	# Add events based on kingdom state
	if kingdom_stats.happiness < 40:
		contextual_events.append({
			"name": "Unrest Brewing", "effects": {"happiness": -10, "military": -5},
			"description": "Whispers of discontent spread through the taverns and markets.",
			"major": true
		})

	if kingdom_stats.treasury > 1500:
		contextual_events.append({
			"name": "Thieves Target Treasury", "effects": {"treasury": -100, "military": -5},
			"description": "Word of the kingdom's wealth attracts unwanted attention from criminal elements."
		})

	if kingdom_stats.military < 30:
		contextual_events.append({
			"name": "Border Skirmish", "effects": {"military": -10, "population": -5},
			"description": "With defenses weak, raiders test your borders.",
			"major": true
		})

	# Add story-phase specific events
	match story_state.main_plot:
		"rising_action":
			contextual_events.append({
				"name": "Mysterious Stranger", "effects": {"happiness": -5},
				"description": "A hooded figure has been asking questions about the royal family in the markets.",
				"major": true
			})
		"climax":
			contextual_events.append({
				"name": "Ominous Portents", "effects": {"happiness": -15},
				"description": "Dark clouds gather unnaturally, and the court wizard speaks of troubling omens.",
				"major": true
			})

	# Add reputation-based events
	if story_state.kingdom_reputation == "unpopular":
		contextual_events.append({
			"name": "Protest in the Square", "effects": {"happiness": -20, "military": -10},
			"description": "Citizens gather in the town square, demanding change from their ruler.",
			"major": true
		})

	return base_events + contextual_events

# Print current kingdom statistics
func print_kingdom_stats():
	print("Kingdom Statistics:")
	print("  Population: %d" % kingdom_stats.population)
	print("  Happiness: %d" % kingdom_stats.happiness)
	print("  Treasury: %d gold" % kingdom_stats.treasury)
	print("  Military: %d" % kingdom_stats.military)
	print("  Food: %d" % kingdom_stats.food)

# Get kingdom context for AI
func get_kingdom_context() -> String:
	return """Population: %d (affects kingdom size and tax income)
Happiness: %d (affects loyalty and productivity)
Treasury: %d gold (affects ability to fund projects)
Military: %d (affects defense and security)
Food: %d (affects population health and growth)

Current Day: %d""" % [
		kingdom_stats.population,
		kingdom_stats.happiness,
		kingdom_stats.treasury,
		kingdom_stats.military,
		kingdom_stats.food,
		current_day
	]

# Check for game over conditions and story endings
func check_game_over():
	var game_over_reason = ""
	var ending_type = ""

	# Critical failure conditions
	if kingdom_stats.population <= 0:
		game_over_reason = "Your kingdom has no people left! The realm is abandoned."
		ending_type = "abandonment"
	elif kingdom_stats.happiness <= 0:
		game_over_reason = "Your people are completely miserable! Revolution has overthrown you!"
		ending_type = "revolution"
	elif kingdom_stats.treasury <= -500:
		game_over_reason = "Your kingdom is bankrupt! You can no longer rule."
		ending_type = "bankruptcy"
	elif kingdom_stats.military <= 0 and kingdom_stats.population > 50:
		game_over_reason = "With no military, your kingdom has been conquered by enemies!"
		ending_type = "conquest"
	elif kingdom_stats.food <= 0 and kingdom_stats.population > 20:
		game_over_reason = "Your people are starving! The kingdom has collapsed from famine."
		ending_type = "famine"

	# Story-driven endings (after day 25)
	if current_day >= 25 and game_over_reason == "":
		var story_ending = determine_story_ending()
		if story_ending != "":
			game_over_reason = story_ending
			ending_type = "story_conclusion"

	if game_over_reason != "":
		game_over = true
		story_state.ending_path = ending_type
		print("=== GAME OVER ===")
		print(game_over_reason)
		print("")
		print_final_story_summary()
		game_ended.emit(game_over_reason)
		return

	# Check for victory conditions (day 30+ with good stats)
	if current_day >= 30:
		var victory_ending = determine_victory_ending()
		if victory_ending != "":
			game_over = true
			story_state.ending_path = "victory"
			print("=== VICTORY ===")
			print(victory_ending)
			print("")
			print_final_story_summary()
			game_ended.emit(victory_ending)

# Determine story-driven ending based on decisions and reputation
func determine_story_ending():
	var reputation = story_state.kingdom_reputation
	var happiness = kingdom_stats.happiness
	var _treasury = kingdom_stats.treasury
	var military = kingdom_stats.military

	# Analyze decision patterns
	var total_decisions = decision_history.size()
	var yes_decisions = 0
	for decision in decision_history:
		if decision.decision == "YES":
			yes_decisions += 1

	var yes_ratio = float(yes_decisions) / float(total_decisions) if total_decisions > 0 else 0.5

	# Story endings based on reputation and stats
	if reputation == "unpopular" and happiness < 30:
		return "The nobles have lost faith in your rule. A council of lords assumes power, ending your reign peacefully but permanently."
	elif reputation == "tyrannical" and military > 80:
		return "Your iron-fisted rule has created a powerful but feared kingdom. You rule through strength, but at what cost to your soul?"
	elif reputation == "beloved" and happiness > 80:
		return "Your compassionate leadership has won the hearts of your people. Though challenges remain, your kingdom enters a golden age of prosperity and peace."
	elif yes_ratio > 0.8:
		return "Your generous nature has bankrupted the kingdom, but the people remember your kindness. You abdicate gracefully, beloved but poor."
	elif yes_ratio < 0.2:
		return "Your stern refusals have kept the kingdom stable, but isolated. You rule over a fortress kingdom, secure but lonely."

	return ""  # Continue playing

# Determine victory ending based on final state
func determine_victory_ending():
	if kingdom_stats.population >= 200 and kingdom_stats.happiness >= 80:
		match story_state.kingdom_reputation:
			"wise_and_prosperous":
				return "Your wisdom and prosperity have created a legendary kingdom! Bards will sing of your reign for generations."
			"beloved":
				return "Your people adore you, and your kingdom thrives! You have achieved the perfect balance of compassion and leadership."
			"generous":
				return "Your generosity has built a kingdom of plenty and joy! Though your treasury is modest, your people's loyalty is priceless."
			_:
				return "Through careful decisions and steady leadership, you have built a thriving kingdom that will endure for ages!"
	elif kingdom_stats.treasury >= 2000 and kingdom_stats.military >= 80:
		return "Your kingdom has become a mighty fortress of wealth and power! Other realms look to you with respect and envy."
	elif kingdom_stats.happiness >= 90:
		return "Your people are the happiest in the known world! Your kingdom may not be the largest, but it is surely the most joyful."

	return ""  # Continue playing

# Print final story summary
func print_final_story_summary():
	print("=== THE STORY OF YOUR REIGN ===")
	print("Days Ruled: %d" % (current_day - 1))
	print("Kingdom Reputation: %s" % story_state.kingdom_reputation.replace("_", " ").capitalize())
	print("Total Decisions Made: %d" % decision_history.size())
	print("")
	print("Major Events of Your Reign:")
	for event in story_state.major_events:
		print("- Day %d: %s" % [event.day, event.description])
	print("")
	print("Final Kingdom Statistics:")
	print_kingdom_stats()
	print("")
	print("Your legacy will be remembered...")

# Public function to handle player decisions (call this from your UI)
func make_decision(decision: String):
	if waiting_for_decision and current_npc != null:
		print("Player chose: %s" % decision)
		player_decision = decision.to_upper()  # Normalize to YES/NO
		waiting_for_decision = false  # This will allow wait_for_player_decision() to continue

# Public function to get current game state
func get_game_state():
	return {
		"day": current_day,
		"stats": kingdom_stats.duplicate(),
		"current_npc": current_npc,
		"current_question": current_question,
		"game_over": game_over
	}
