extends Node

# References to other managers
@onready var ai = Player2
@onready var character_manager = CharacterManager

# Game state variables
var current_day = 1
var kingdom_stats = {
	"population": 100,
	"happiness": 50,
	"treasury": 1000,
	"military": 50,
	"food": 50
}

var current_npc = null
var current_question = ""
var current_context = ""
var npcs_today = []
var npcs_per_day = 5
var game_over = false

# Signals for UI updates (you can connect these to your UI)
signal stats_updated(stats)
signal new_question(npc_name, question, context)
signal day_ended(day_number)
signal game_ended(reason)

func _ready():
	print("=== SORT THE COURT: AI EDITION ===")
	print("You are the King! Make wise decisions to keep your kingdom prosperous.")
	print("Answer YES or NO to each petitioner's request.")
	print("")
	await start_new_day()

# Start a new day with fresh NPCs
func start_new_day():
	print("=== DAY %d ===" % current_day)
	print_kingdom_stats()
	print("")

	npcs_today.clear()

	# Generate NPCs for today
	for i in range(npcs_per_day):
		var npc = await generate_random_npc()
		npcs_today.append(npc)

	# Start processing NPCs
	await process_daily_npcs()

# Generate a random NPC with AI
func generate_random_npc():
	var npc_types = [
		"merchant", "farmer", "soldier", "noble", "peasant",
		"wizard", "priest", "thief", "diplomat", "inventor"
	]

	var npc_type = npc_types.pick_random()
	var npc_name = await generate_npc_name(npc_type)

	# Create character description for the AI
	var description = create_npc_description(npc_type, npc_name)

	# Create the character in character manager
	character_manager.create_new_character(npc_name, description)

	return {
		"name": npc_name,
		"type": npc_type,
		"description": description
	}

# Generate NPC name using AI
func generate_npc_name(npc_type: String) -> String:
	var name_prompt = "Generate a single medieval fantasy name for a %s. Respond with only the name, nothing else." % npc_type

	var result = await ai.create_chat_completion([
		{"role": "system", "content": name_prompt}
	])

	if result and result.has("message"):
		return result["message"]["content"].strip_edges()
	else:
		# Fallback names if AI fails
		var fallback_names = ["Aldric", "Beatrice", "Cedric", "Diana", "Edmund", "Fiona"]
		return fallback_names.pick_random()

# Create detailed NPC description for AI roleplay
func create_npc_description(npc_type: String, npc_name: String) -> String:
	var base_context = get_kingdom_context()

	var description = """You are %s, a %s in a medieval kingdom.

CURRENT KINGDOM STATE:
%s

ROLEPLAY INSTRUCTIONS:
- You will present ONE specific request or problem to the King
- Your request should be a YES/NO question that affects the kingdom
- Stay in character as a %s
- Be concise but engaging (2-3 sentences max)
- Your request should have clear consequences for kingdom stats
- Consider the current state of the kingdom in your request
- End your message with a clear question that can be answered with YES or NO

EXAMPLE FORMAT:
"Your Majesty, [brief situation]. [your request/question that requires YES or NO]?"

Remember: You are speaking directly to the King. Be respectful but present your case clearly.""" % [npc_name, npc_type, base_context, npc_type]

	return description

# Process all NPCs for the day
func process_daily_npcs():
	for npc in npcs_today:
		if game_over:
			break

		await present_npc_to_king(npc)

		# Small delay between NPCs
		await get_tree().create_timer(1.0).timeout

	# End of day
	await end_day()

# Present an NPC to the king and get their request
func present_npc_to_king(npc):
	current_npc = npc
	print("--- %s the %s approaches the throne ---" % [npc.name, npc.type])

	# Get the character from character manager
	var character = null
	for char in character_manager.characters:
		if char.name == npc.name:
			character = char
			break

	if not character:
		print("Error: Character not found!")
		return

	# Get AI-generated request
	var ai_response = await ai.create_chat_completion(character.chat_history)

	if not ai_response or not ai_response.has("message"):
		print("Error: Could not get AI response!")
		return

	var npc_message = ai_response["message"]["content"]
	current_question = npc_message
	current_context = npc.description

	print("%s says:" % npc.name)
	print('"%s"' % npc_message)
	print("")
	print("What is your decision, Your Majesty? (YES/NO)")

	# Emit signal for UI
	new_question.emit(npc.name, npc_message, current_context)

	# Wait for player input
	await wait_for_player_decision()

# Wait for player input (YES/NO)
func wait_for_player_decision():
	# In a real game, this would wait for UI input
	# For now, we'll simulate with a timer and random choice for demo
	print("(Waiting for your decision...)")

	# You can replace this with actual input handling
	await get_tree().create_timer(2.0).timeout

	# For demo purposes, let's make a random decision
	# In your actual game, replace this with real player input
	var decision = ["YES", "NO"].pick_random()
	print("King decides: %s" % decision)
	print("")

	await process_decision(decision)

# Process the king's decision
func process_decision(decision: String):
	var is_yes = decision.to_upper() == "YES"

	# Add the decision to character's chat history
	var character = null
	for char in character_manager.characters:
		if char.name == current_npc.name:
			character = char
			break

	if character:
		character.chat_history.append({
			"role": "user",
			"content": "The King's decision: %s" % decision
		})

	# Apply consequences based on NPC type and decision
	apply_decision_consequences(current_npc, is_yes)

	# Update stats display
	print("Kingdom stats after decision:")
	print_kingdom_stats()
	print("")

	# Check for game over conditions
	check_game_over()

# Apply consequences of the decision to kingdom stats
func apply_decision_consequences(npc, is_yes: bool):
	# This is a simplified consequence system
	# You can make this much more sophisticated based on the actual AI request

	var stat_changes = {}

	match npc.type:
		"merchant":
			if is_yes:
				stat_changes = {"treasury": 50, "happiness": 10}
			else:
				stat_changes = {"treasury": -20, "happiness": -5}

		"farmer":
			if is_yes:
				stat_changes = {"food": 20, "treasury": -30}
			else:
				stat_changes = {"food": -10, "happiness": -10}

		"soldier":
			if is_yes:
				stat_changes = {"military": 15, "treasury": -40}
			else:
				stat_changes = {"military": -10, "happiness": -5}

		"noble":
			if is_yes:
				stat_changes = {"happiness": 15, "treasury": -50}
			else:
				stat_changes = {"happiness": -15, "population": -5}

		"peasant":
			if is_yes:
				stat_changes = {"happiness": 20, "treasury": -20}
			else:
				stat_changes = {"happiness": -20, "population": -10}

		"wizard":
			if is_yes:
				stat_changes = {"happiness": 10, "treasury": -60}
			else:
				stat_changes = {"happiness": -10}

		"priest":
			if is_yes:
				stat_changes = {"happiness": 25, "treasury": -30}
			else:
				stat_changes = {"happiness": -25}

		"thief":
			if is_yes:
				stat_changes = {"treasury": -30, "happiness": -10}
			else:
				stat_changes = {"military": 5, "happiness": 5}

		"diplomat":
			if is_yes:
				stat_changes = {"happiness": 15, "military": 10}
			else:
				stat_changes = {"happiness": -10, "military": -5}

		"inventor":
			if is_yes:
				stat_changes = {"happiness": 10, "treasury": -40}
			else:
				stat_changes = {"happiness": -5}

	# Apply the changes
	for stat in stat_changes:
		kingdom_stats[stat] += stat_changes[stat]
		kingdom_stats[stat] = max(0, kingdom_stats[stat])  # Don't go below 0

	# Emit signal for UI updates
	stats_updated.emit(kingdom_stats)

# End the current day
func end_day():
	print("=== END OF DAY %d ===" % current_day)
	print("Final kingdom stats:")
	print_kingdom_stats()
	print("")

	# Random events at end of day
	await apply_random_daily_events()

	# Emit day ended signal
	day_ended.emit(current_day)

	current_day += 1

	if not game_over:
		print("Press any key to continue to the next day...")
		await get_tree().create_timer(3.0).timeout  # Auto-continue for demo
		await start_new_day()

# Apply random events that happen at the end of each day
func apply_random_daily_events():
	# 30% chance of a random event
	if randf() < 0.3:
		var events = [
			{"name": "Good Harvest", "effects": {"food": 30, "happiness": 10}},
			{"name": "Merchant Caravan", "effects": {"treasury": 100}},
			{"name": "Plague Outbreak", "effects": {"population": -20, "happiness": -15}},
			{"name": "Bandit Attack", "effects": {"treasury": -50, "military": -10}},
			{"name": "Festival Day", "effects": {"happiness": 20, "treasury": -30}},
			{"name": "Tax Collection", "effects": {"treasury": 80, "happiness": -10}}
		]

		var event = events.pick_random()
		print("RANDOM EVENT: %s!" % event.name)

		for stat in event.effects:
			kingdom_stats[stat] += event.effects[stat]
			kingdom_stats[stat] = max(0, kingdom_stats[stat])

		print("Effects applied to kingdom.")
		print_kingdom_stats()
		print("")

# Print current kingdom statistics
func print_kingdom_stats():
	print("Kingdom Statistics:")
	print("  Population: %d" % kingdom_stats.population)
	print("  Happiness: %d" % kingdom_stats.happiness)
	print("  Treasury: %d gold" % kingdom_stats.treasury)
	print("  Military: %d" % kingdom_stats.military)
	print("  Food: %d" % kingdom_stats.food)

# Get kingdom context for AI
func get_kingdom_context() -> String:
	return """Population: %d (affects kingdom size and tax income)
Happiness: %d (affects loyalty and productivity)
Treasury: %d gold (affects ability to fund projects)
Military: %d (affects defense and security)
Food: %d (affects population health and growth)

Current Day: %d""" % [
		kingdom_stats.population,
		kingdom_stats.happiness,
		kingdom_stats.treasury,
		kingdom_stats.military,
		kingdom_stats.food,
		current_day
	]

# Check for game over conditions
func check_game_over():
	var game_over_reason = ""

	if kingdom_stats.population <= 0:
		game_over_reason = "Your kingdom has no people left! The realm is abandoned."
	elif kingdom_stats.happiness <= 0:
		game_over_reason = "Your people are completely miserable! Revolution has overthrown you!"
	elif kingdom_stats.treasury <= -500:
		game_over_reason = "Your kingdom is bankrupt! You can no longer rule."
	elif kingdom_stats.military <= 0 and kingdom_stats.population > 50:
		game_over_reason = "With no military, your kingdom has been conquered by enemies!"
	elif kingdom_stats.food <= 0 and kingdom_stats.population > 20:
		game_over_reason = "Your people are starving! The kingdom has collapsed from famine."

	if game_over_reason != "":
		game_over = true
		print("=== GAME OVER ===")
		print(game_over_reason)
		print("You ruled for %d days." % (current_day - 1))
		print("Final Stats:")
		print_kingdom_stats()
		game_ended.emit(game_over_reason)
		return

	# Check for victory conditions
	if current_day > 30 and kingdom_stats.population >= 200 and kingdom_stats.happiness >= 80:
		game_over = true
		print("=== VICTORY ===")
		print("Congratulations! You have built a thriving kingdom!")
		print("Your wise rule has lasted %d days." % (current_day - 1))
		print("Final Stats:")
		print_kingdom_stats()
		game_ended.emit("Victory! Your kingdom prospers!")

# Public function to handle player decisions (call this from your UI)
func make_decision(decision: String):
	if current_npc != null:
		await process_decision(decision)

# Public function to get current game state
func get_game_state():
	return {
		"day": current_day,
		"stats": kingdom_stats.duplicate(),
		"current_npc": current_npc,
		"current_question": current_question,
		"game_over": game_over
	}
