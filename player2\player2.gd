extends Node

@onready var http_request: HTTPRequest = HTTPRequest.new()

var server_ok := false
var game_id = 1

# Simple rate limiting to prevent 429 errors
var last_request_time = 0.0
var min_request_interval = 2.0  # 2 seconds between requests to be safe

func _ready():
	add_child(http_request)
	server_ok = await get_server_health()
	if server_ok:
		print("AI Server is ready!")
		# Test the API with the correct format
		var test_result = await create_chat_completion([{"role": "user", "content": "what is your name?"}], "Your name is <PERSON>")
		print("Test result: " + test_result)

func get_server_health() -> bool:
	http_request.request("http://127.0.0.1:4315/v1/health")
	var result = await http_request.request_completed
	var json = _process_result(result)
	return json.has("client_version") and json["client_version"] is String

# Simple, clean implementation based on API documentation
func create_chat_completion(character_history: Array = [], additional_context: String = ""):
	# Wait for rate limiting
	await _wait_for_rate_limit()

	# Build messages array according to API documentation
	var messages = []

	# Add system context first if provided
	if additional_context != "":
		messages.append({"role": "system", "content": additional_context})

	# Add character history
	for message in character_history:
		messages.append(message)

	# Create request body exactly as shown in API documentation
	var request_body = {
		"messages": messages,
		"stream": false
	}

	# Convert to JSON string properly
	var json_string = JSON.stringify(request_body)
	var headers = ["Content-Type: application/json"]

	print("Making AI request with %d messages" % messages.size())

	# Make the request
	http_request.request("http://127.0.0.1:4315/v1/chat/completions", headers, HTTPClient.METHOD_POST, json_string)
	var result = await http_request.request_completed

	# Update last request time for rate limiting
	last_request_time = Time.get_unix_time_from_system()

	# Process the result
	var json = _process_result(result)

	# Extract content according to API documentation
	if json.has("choices") and json["choices"].size() > 0:
		var choice = json["choices"][0]
		if choice.has("message") and choice["message"].has("content"):
			return choice["message"]["content"]

	print("AI request failed or returned empty response")
	return ""

# Simple rate limiting
func _wait_for_rate_limit():
	var current_time = Time.get_unix_time_from_system()
	var time_since_last = current_time - last_request_time

	if time_since_last < min_request_interval:
		var wait_time = min_request_interval - time_since_last
		print("Rate limiting: waiting %.1f seconds" % wait_time)
		await get_tree().create_timer(wait_time).timeout

func _process_result(result: Array):
	var response_code = result[1]
	var body_bytes = result[3]
	var body_text = body_bytes.get_string_from_utf8()
	if response_code != 200 and response_code != 201:
		push_warning("HTTP Error: %s" % response_code)
		return {}
	var parsed = JSON.parse_string(body_text)
	if parsed == null:
		push_warning("JSON Parse Error")
		return {}
	return parsed
