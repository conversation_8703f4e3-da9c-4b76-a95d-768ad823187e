extends Node

@onready var http_request: HTTPRequest = HTTPRequest.new()

var server_ok := false
var game_id = 1

# Rate limiting to prevent 429 errors
var last_request_time = 0.0
var min_request_interval = 1.5  # Minimum 1.5 seconds between requests (increased for safety)
var request_queue = []
var processing_queue = false
var max_retries = 2  # Maximum number of retries for failed requests

func _ready():
	add_child(http_request)
	server_ok = await get_server_health()
	if server_ok:
		print(await create_chat_completion([{"role": "user", "content": "what is your name?"}], "Your name is <PERSON>"))

func get_server_health() -> bool:
	http_request.request("http://127.0.0.1:4315/v1/health")
	var result = await http_request.request_completed
	var json = _process_result(result)
	return json["client_version"] is String

func create_chat_completion(character_history: Array = [], additional_context: String = ""):
	# Add to queue to handle rate limiting
	return await _queued_chat_completion(character_history, additional_context)

# Rate-limited chat completion
func _queued_chat_completion(character_history: Array, additional_context: String):
	# Add request to queue
	var request_data = {
		"history": character_history.duplicate(),
		"context": additional_context,
		"completed": false,
		"result": ""
	}
	request_queue.append(request_data)

	# Start processing queue if not already running
	if not processing_queue:
		_process_request_queue()

	# Wait for this request to be completed
	while not request_data.completed:
		await get_tree().process_frame

	return request_data.result

# Process requests with rate limiting
func _process_request_queue():
	processing_queue = true

	while request_queue.size() > 0:
		var current_time = Time.get_unix_time_from_system()
		var time_since_last = current_time - last_request_time

		# Wait if we need to respect rate limit
		if time_since_last < min_request_interval:
			var wait_time = min_request_interval - time_since_last
			await get_tree().create_timer(wait_time).timeout

		# Process next request
		var request_data = request_queue.pop_front()
		var result = await _execute_chat_completion(request_data.history, request_data.context)

		request_data.result = result
		request_data.completed = true
		last_request_time = Time.get_unix_time_from_system()

	processing_queue = false

# Execute the actual chat completion with retry logic
func _execute_chat_completion(character_history: Array, additional_context: String):
	await get_tree().create_timer(2.0).timeout  # Wait 2 seconds before retry
	var messages = character_history.duplicate()

	if additional_context != "":
		messages.append({"role": "system", "content": additional_context})

	var sample_data = {
		"messages": messages,
		"stream": false
	}

	# Try the request with retries
	for attempt in range(max_retries + 1):
		http_request.request("http://127.0.0.1:4315/v1/chat/completions", ["Content-Type: application/json"], HTTPClient.METHOD_POST, str(sample_data))
		var result = await http_request.request_completed
		var json = _process_result(result)

		if json.has("choices") and json["choices"].size() > 0:
			return json["choices"][0]["message"]["content"]

		# If this wasn't the last attempt, wait before retrying
		if attempt < max_retries:
			print("AI request failed (attempt %d/%d), retrying..." % [attempt + 1, max_retries + 1])
			await get_tree().create_timer(2.0).timeout  # Wait 2 seconds before retry

	print("AI request failed after %d attempts" % (max_retries + 1))
	return ""

func _process_result(result: Array):
	var response_code = result[1]
	var body_bytes = result[3]
	var body_text = body_bytes.get_string_from_utf8()
	if response_code != 200 and response_code != 201:
		push_warning("HTTP Error: %s" % response_code)
		return {}
	var parsed = JSON.parse_string(body_text)
	if parsed == null:
		push_warning("JSON Parse Error")
		return {}
	return parsed
