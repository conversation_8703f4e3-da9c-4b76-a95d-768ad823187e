extends Node

@onready var http_request: HTTPRequest = HTTPRequest.new()

var server_ok := false
var game_id = 1

func _ready():
	add_child(http_request)
	server_ok = await get_server_health()
	if server_ok:
		print(await create_chat_completion())

func get_server_health() -> bool:
	http_request.request("http://127.0.0.1:4315/v1/health")
	var result = await http_request.request_completed
	var json = _process_result(result)
	return json["client_version"] is String

func create_chat_completion(character_history: Array = [], additional_context: String = ""):
	if additional_context != "":
		character_history.append({"role": "system", "content": additional_context})
	var sample_data = {
		"messages": character_history,
		"stream": false
	}
	http_request.request("http://127.0.0.1:4315/v1/chat/completions", ["Content-Type: application/json"], HTTPClient.METHOD_POST, str(sample_data))
	var result = await http_request.request_completed
	var json = _process_result(result)
	return json["choices"].pick_random()

func _process_result(result: Array):
	var response_code = result[1]
	var body_bytes = result[3]
	var body_text = body_bytes.get_string_from_utf8()
	if response_code != 200 and response_code != 201:
		push_warning("HTTP Error: %s" % response_code)
		return {}
	var parsed = JSON.parse_string(body_text)
	if parsed == null:
		push_warning("JSON Parse Error")
		return {}
	return parsed
